# SQL Server Database Configuration
# Copy this file to .env and update with your SQL Server details

# Database Connection
DB_HOST=localhost
DB_PORT=1433
DB_NAME=doaxvv_handbook
DB_USER=doaxvv_user
DB_PASSWORD=doaxvv_password

# For migration from PostgreSQL (optional)
PG_HOST=localhost
PG_PORT=5432
PG_NAME=doaxvv_handbook
PG_USER=doaxvv_user
PG_PASSWORD=doaxvv_password

# Application Settings
NODE_ENV=development
PORT=3001
FRONTEND_URL=http://localhost:5173

# Logging
LOG_LEVEL=info

# Security (for production)
# DB_ENCRYPT=true
# DB_TRUST_SERVER_CERTIFICATE=false
