import { Request, Response, NextFunction } from 'express';
import <PERSON><PERSON> from 'joi';
import logger from '../config/logger';

export const validate = (schema: Joi.ObjectSchema) => {
  return (req: Request, res: Response, next: NextFunction): void => {
    const { error } = schema.validate(req.body);

    if (error) {
      logger.warn('Validation error:', error.details[0].message);
      res.status(400).json({
        success: false,
        error: 'Validation error',
        details: error.details[0].message
      });
      return;
    }

    next();
  };
};

export const validateParams = (schema: Joi.ObjectSchema) => {
  return (req: Request, res: Response, next: NextFunction): void => {
    const { error } = schema.validate(req.params);

    if (error) {
      logger.warn('Parameter validation error:', error.details[0].message);
      res.status(400).json({
        success: false,
        error: 'Parameter validation error',
        details: error.details[0].message
      });
      return;
    }

    next();
  };
};

export const validateQuery = (schema: Joi.ObjectSchema) => {
  return (req: Request, res: Response, next: NextFunction): void => {
    const { error } = schema.validate(req.query);

    if (error) {
      logger.warn('Query validation error:', error.details[0].message);
      res.status(400).json({
        success: false,
        error: 'Query validation error',
        details: error.details[0].message
      });
      return;
    }

    next();
  };
};

// Common validation schemas
export const schemas = {
  // UUID validation
  uuid: Joi.object({
    id: Joi.string().uuid().required()
  }),

  // Character schemas
  createCharacter: Joi.object({
    id: Joi.string().required(),
    name: Joi.string().min(1).max(255).required(),
    nameJp: Joi.string().max(255).optional(),
    nameEn: Joi.string().max(255).optional(),
    nameZh: Joi.string().max(255).optional()
  }),

  updateCharacter: Joi.object({
    name: Joi.string().min(1).max(255).optional(),
    nameJp: Joi.string().max(255).optional(),
    nameEn: Joi.string().max(255).optional(),
    nameZh: Joi.string().max(255).optional()
  }),

  // Skill schemas
  createSkill: Joi.object({
    id: Joi.string().required(),
    name: Joi.string().min(1).max(255).required(),
    type: Joi.string().required(),
    description: Joi.string().optional(),
    icon: Joi.string().optional()
  }),

  updateSkill: Joi.object({
    name: Joi.string().min(1).max(255).optional(),
    type: Joi.string().optional(),
    description: Joi.string().optional(),
    icon: Joi.string().optional()
  }),

  // Swimsuit schemas
  createSwimsuit: Joi.object({
    id: Joi.string().required(),
    name: Joi.string().min(1).max(255).required(),
    characterId: Joi.string().required(),
    rarity: Joi.string().valid('SSR', 'SR', 'R').required(),
    pow: Joi.number().integer().min(0).required(),
    tec: Joi.number().integer().min(0).required(),
    stm: Joi.number().integer().min(0).required(),
    apl: Joi.number().integer().min(0).required(),
    releaseDate: Joi.date().required(),
    reappearDate: Joi.date().optional(),
    image: Joi.string().optional()
  }),

  updateSwimsuit: Joi.object({
    name: Joi.string().min(1).max(255).optional(),
    characterId: Joi.string().optional(),
    rarity: Joi.string().valid('SSR', 'SR', 'R').optional(),
    pow: Joi.number().integer().min(0).optional(),
    tec: Joi.number().integer().min(0).optional(),
    stm: Joi.number().integer().min(0).optional(),
    apl: Joi.number().integer().min(0).optional(),
    releaseDate: Joi.date().optional(),
    reappearDate: Joi.date().optional(),
    image: Joi.string().optional()
  }),

  // Girl schemas
  createGirl: Joi.object({
    id: Joi.string().required(),
    name: Joi.string().min(1).max(255).required(),
    type: Joi.string().valid('pow', 'tec', 'stm').required(),
    level: Joi.number().integer().min(1).max(100).required(),
    pow: Joi.number().integer().min(0).required(),
    tec: Joi.number().integer().min(0).required(),
    stm: Joi.number().integer().min(0).required(),
    apl: Joi.number().integer().min(0).required(),
    maxPow: Joi.number().integer().min(0).required(),
    maxTec: Joi.number().integer().min(0).required(),
    maxStm: Joi.number().integer().min(0).required(),
    maxApl: Joi.number().integer().min(0).required(),
    birthday: Joi.date().required(),
    swimsuitId: Joi.string().optional()
  }),

  updateGirl: Joi.object({
    name: Joi.string().min(1).max(255).optional(),
    type: Joi.string().valid('pow', 'tec', 'stm').optional(),
    level: Joi.number().integer().min(1).max(100).optional(),
    pow: Joi.number().integer().min(0).optional(),
    tec: Joi.number().integer().min(0).optional(),
    stm: Joi.number().integer().min(0).optional(),
    apl: Joi.number().integer().min(0).optional(),
    maxPow: Joi.number().integer().min(0).optional(),
    maxTec: Joi.number().integer().min(0).optional(),
    maxStm: Joi.number().integer().min(0).optional(),
    maxApl: Joi.number().integer().min(0).optional(),
    birthday: Joi.date().optional(),
    swimsuitId: Joi.string().optional()
  }),

  // Accessory schemas
  createAccessory: Joi.object({
    id: Joi.string().required(),
    name: Joi.string().min(1).max(255).required(),
    type: Joi.string().valid('head', 'face', 'hand').required(),
    skillId: Joi.string().required(),
    pow: Joi.number().integer().min(0).optional(),
    tec: Joi.number().integer().min(0).optional(),
    stm: Joi.number().integer().min(0).optional(),
    apl: Joi.number().integer().min(0).optional()
  }),

  updateAccessory: Joi.object({
    name: Joi.string().min(1).max(255).optional(),
    type: Joi.string().valid('head', 'face', 'hand').optional(),
    skillId: Joi.string().optional(),
    pow: Joi.number().integer().min(0).optional(),
    tec: Joi.number().integer().min(0).optional(),
    stm: Joi.number().integer().min(0).optional(),
    apl: Joi.number().integer().min(0).optional()
  }),

  // Venus Board schemas
  createVenusBoard: Joi.object({
    girlId: Joi.string().required(),
    pow: Joi.number().integer().min(0).required(),
    tec: Joi.number().integer().min(0).required(),
    stm: Joi.number().integer().min(0).required(),
    apl: Joi.number().integer().min(0).required()
  }),

  updateVenusBoard: Joi.object({
    pow: Joi.number().integer().min(0).optional(),
    tec: Joi.number().integer().min(0).optional(),
    stm: Joi.number().integer().min(0).optional(),
    apl: Joi.number().integer().min(0).optional()
  }),

  // Event schemas
  createEvent: Joi.object({
    id: Joi.string().required(),
    name: Joi.string().min(1).max(255).required(),
    type: Joi.string().valid('festival', 'gacha', 'ranking', 'mission', 'collab').required(),
    description: Joi.string().optional(),
    startDate: Joi.date().required(),
    endDate: Joi.date().required(),
    image: Joi.string().optional(),
    isActive: Joi.boolean().default(false),
    rewards: Joi.any().optional(),
    requirements: Joi.any().optional()
  }),

  updateEvent: Joi.object({
    name: Joi.string().min(1).max(255).optional(),
    type: Joi.string().valid('festival', 'gacha', 'ranking', 'mission', 'collab').optional(),
    description: Joi.string().optional(),
    startDate: Joi.date().optional(),
    endDate: Joi.date().optional(),
    image: Joi.string().optional(),
    isActive: Joi.boolean().optional(),
    rewards: Joi.any().optional(),
    requirements: Joi.any().optional()
  }),

  // Bromide schemas
  createBromide: Joi.object({
    id: Joi.string().required(),
    name: Joi.string().min(1).max(255).required(),
    type: Joi.string().valid('Character', 'Scene', 'Frame', 'Background', 'Sticker', 'Effect').required(),
    rarity: Joi.string().valid('N', 'R', 'SR', 'SSR', 'UR').required(),
    description: Joi.string().optional(),
    characterId: Joi.string().optional(),
    effects: Joi.any().optional(),
    source: Joi.string().optional(),
    image: Joi.string().optional()
  }),

  updateBromide: Joi.object({
    name: Joi.string().min(1).max(255).optional(),
    type: Joi.string().valid('Character', 'Scene', 'Frame', 'Background', 'Sticker', 'Effect').optional(),
    rarity: Joi.string().valid('N', 'R', 'SR', 'SSR', 'UR').optional(),
    description: Joi.string().optional(),
    characterId: Joi.string().optional(),
    effects: Joi.any().optional(),
    source: Joi.string().optional(),
    image: Joi.string().optional()
  }),

  // Document schemas
  createDocument: Joi.object({
    id: Joi.string().required(),
    title: Joi.string().min(1).max(500).required(),
    content: Joi.string().required(),
    category: Joi.string().required(),
    tags: Joi.array().items(Joi.string()).optional(),
    author: Joi.string().required(),
    isPublished: Joi.boolean().default(false)
  }),

  updateDocument: Joi.object({
    title: Joi.string().min(1).max(500).optional(),
    content: Joi.string().optional(),
    category: Joi.string().optional(),
    tags: Joi.array().items(Joi.string()).optional(),
    author: Joi.string().optional(),
    isPublished: Joi.boolean().optional()
  }),

  // Memory schemas
  createMemory: Joi.object({
    id: Joi.string().required(),
    title: Joi.string().min(1).max(500).required(),
    description: Joi.string().optional(),
    type: Joi.string().valid('photo', 'video').required(),
    date: Joi.date().required(),
    characters: Joi.array().items(Joi.string()).optional(),
    tags: Joi.array().items(Joi.string()).optional(),
    thumbnail: Joi.string().optional(),
    favorite: Joi.boolean().default(false),
    unlocked: Joi.boolean().default(true)
  }),

  updateMemory: Joi.object({
    title: Joi.string().min(1).max(500).optional(),
    description: Joi.string().optional(),
    type: Joi.string().valid('photo', 'video').optional(),
    date: Joi.date().optional(),
    characters: Joi.array().items(Joi.string()).optional(),
    tags: Joi.array().items(Joi.string()).optional(),
    thumbnail: Joi.string().optional(),
    favorite: Joi.boolean().optional(),
    unlocked: Joi.boolean().optional()
  }),

  // Shop Item schemas
  createShopItem: Joi.object({
    id: Joi.string().required(),
    name: Joi.string().min(1).max(255).required(),
    description: Joi.string().optional(),
    type: Joi.string().valid('swimsuit', 'accessory', 'decoration', 'currency', 'booster').required(),
    category: Joi.string().required(),
    section: Joi.string().valid('owner', 'event', 'venus', 'vip').required(),
    price: Joi.number().integer().min(0).required(),
    currency: Joi.string().valid('coins', 'gems', 'tickets').required(),
    rarity: Joi.string().valid('common', 'rare', 'epic', 'legendary').required(),
    image: Joi.string().optional(),
    inStock: Joi.boolean().default(true),
    isNew: Joi.boolean().default(false),
    discount: Joi.number().integer().min(0).max(100).optional(),
    limitedTime: Joi.boolean().default(false),
    featured: Joi.boolean().default(false)
  }),

  updateShopItem: Joi.object({
    name: Joi.string().min(1).max(255).optional(),
    description: Joi.string().optional(),
    type: Joi.string().valid('swimsuit', 'accessory', 'decoration', 'currency', 'booster').optional(),
    category: Joi.string().optional(),
    section: Joi.string().valid('owner', 'event', 'venus', 'vip').optional(),
    price: Joi.number().integer().min(0).optional(),
    currency: Joi.string().valid('coins', 'gems', 'tickets').optional(),
    rarity: Joi.string().valid('common', 'rare', 'epic', 'legendary').optional(),
    image: Joi.string().optional(),
    inStock: Joi.boolean().optional(),
    isNew: Joi.boolean().optional(),
    discount: Joi.number().integer().min(0).max(100).optional(),
    limitedTime: Joi.boolean().optional(),
    featured: Joi.boolean().optional()
  }),

  // Pagination
  pagination: Joi.object({
    page: Joi.number().integer().min(1).default(1),
    limit: Joi.number().integer().min(1).max(100).default(10),
    sortBy: Joi.string().optional(),
    sortOrder: Joi.string().valid('asc', 'desc').default('asc'),
    type: Joi.string().optional(),
    character: Joi.string().optional(),
    category: Joi.string().optional(),
    section: Joi.string().optional(),
    rarity: Joi.string().optional(),
    featured: Joi.boolean().optional(),
    inStock: Joi.boolean().optional()
  })
};