import { Router } from 'express';
import { validate, validateQuery, schemas } from '../middleware/validation';
import { asyncHandler } from '../middleware/errorHandler';
import databaseService from '../services/DatabaseService';
import logger from '../config/logger';

const router = Router();

// GET /api/documents - Get all documents with pagination
router.get('/', 
  validateQuery(schemas.pagination),
  asyncHandler(async (req, res) => {
    const { page = 1, limit = 10, sortBy, sortOrder, category, published } = req.query;
    
    let result;
    
    if (published === 'true') {
      result = await databaseService.getPublishedDocuments({
        page: Number(page),
        limit: Number(limit),
        sortBy: sortBy as string,
        sortOrder: sortOrder as 'asc' | 'desc'
      });
    } else if (category) {
      result = await databaseService.getDocumentsByCategory(category as string, {
        page: Number(page),
        limit: Number(limit),
        sortBy: sortBy as string,
        sortOrder: sortOrder as 'asc' | 'desc'
      });
    } else {
      result = await databaseService.getDocuments({
        page: Number(page),
        limit: Number(limit),
        sortBy: sortBy as string,
        sortOrder: sortOrder as 'asc' | 'desc'
      });
    }

    logger.info(`Retrieved ${result.data.length} documents for page ${page}`);

    res.json({
      success: true,
      data: result.data,
      pagination: result.pagination
    });
  })
);

// GET /api/documents/categories - Get document categories
router.get('/categories',
  asyncHandler(async (req, res) => {
    const categories = [
      { id: 'tutorial', name: 'Tutorial', color: '#3B82F6' },
      { id: 'reference', name: 'Reference', color: '#10B981' },
      { id: 'gameplay', name: 'Gameplay', color: '#F59E0B' },
      { id: 'update', name: 'Updates', color: '#EF4444' },
      { id: 'community', name: 'Community', color: '#8B5CF6' }
    ];
    
    res.json({
      success: true,
      data: categories
    });
  })
);

// GET /api/documents/:id - Get document by ID
router.get('/:id',
  asyncHandler(async (req, res) => {
    const { id } = req.params;
    
    const document = await databaseService.getDocumentById(id);
    
    logger.info(`Retrieved document: ${document.title}`);

    res.json({
      success: true,
      data: document
    });
  })
);

// POST /api/documents - Create new document
router.post('/',
  validate(schemas.createDocument),
  asyncHandler(async (req, res) => {
    const document = await databaseService.createDocument(req.body);
    
    logger.info(`Created document: ${document.title}`);

    res.status(201).json({
      success: true,
      data: document,
      message: 'Document created successfully'
    });
  })
);

// PUT /api/documents/:id - Update document
router.put('/:id',
  validate(schemas.updateDocument),
  asyncHandler(async (req, res) => {
    const { id } = req.params;
    
    const document = await databaseService.updateDocument(id, req.body);
    
    logger.info(`Updated document: ${document.title}`);

    res.json({
      success: true,
      data: document,
      message: 'Document updated successfully'
    });
  })
);

// DELETE /api/documents/:id - Delete document
router.delete('/:id',
  asyncHandler(async (req, res) => {
    const { id } = req.params;
    
    await databaseService.deleteDocument(id);
    
    logger.info(`Deleted document with ID: ${id}`);

    res.json({
      success: true,
      message: 'Document deleted successfully'
    });
  })
);

export default router;
