// Database entity types for the backend service

export interface Character {
  id: string;
  name: string;
  nameJp?: string;
  nameEn?: string;
  nameZh?: string;
  createdAt?: Date;
  updatedAt?: Date;
}

export interface NewCharacter {
  id: string;
  name: string;
  nameJp?: string;
  nameEn?: string;
  nameZh?: string;
}

export interface Skill {
  id: string;
  name: string;
  type: string;
  description?: string;
  icon?: string;
  createdAt?: Date;
  updatedAt?: Date;
}

export interface NewSkill {
  id: string;
  name: string;
  type: string;
  description?: string;
  icon?: string;
}

export interface Swimsuit {
  id: string;
  name: string;
  characterId: string;
  rarity: 'SSR' | 'SR' | 'R';
  pow: number;
  tec: number;
  stm: number;
  apl: number;
  releaseDate: Date;
  reappearDate?: Date;
  image?: string;
  createdAt?: Date;
  updatedAt?: Date;
}

export interface NewSwimsuit {
  id: string;
  name: string;
  characterId: string;
  rarity: 'SSR' | 'SR' | 'R';
  pow: number;
  tec: number;
  stm: number;
  apl: number;
  releaseDate: Date;
  reappearDate?: Date;
  image?: string;
}

export interface Girl {
  id: string;
  name: string;
  type: 'pow' | 'tec' | 'stm';
  level: number;
  pow: number;
  tec: number;
  stm: number;
  apl: number;
  maxPow: number;
  maxTec: number;
  maxStm: number;
  maxApl: number;
  birthday: Date;
  swimsuitId?: string;
  createdAt?: Date;
  updatedAt?: Date;
}

export interface NewGirl {
  id: string;
  name: string;
  type: 'pow' | 'tec' | 'stm';
  level: number;
  pow: number;
  tec: number;
  stm: number;
  apl: number;
  maxPow: number;
  maxTec: number;
  maxStm: number;
  maxApl: number;
  birthday: Date;
  swimsuitId?: string;
}

export interface Accessory {
  id: string;
  name: string;
  type: 'head' | 'face' | 'hand';
  skillId: string;
  pow?: number;
  tec?: number;
  stm?: number;
  apl?: number;
  createdAt?: Date;
  updatedAt?: Date;
}

export interface NewAccessory {
  id: string;
  name: string;
  type: 'head' | 'face' | 'hand';
  skillId: string;
  pow?: number;
  tec?: number;
  stm?: number;
  apl?: number;
}

export interface VenusBoard {
  id: number;
  girlId: string;
  pow: number;
  tec: number;
  stm: number;
  apl: number;
  createdAt?: Date;
  updatedAt?: Date;
}

export interface NewVenusBoard {
  girlId: string;
  pow: number;
  tec: number;
  stm: number;
  apl: number;
}

// Events
export interface Event {
  id: string;
  name: string;
  type: 'festival' | 'gacha' | 'ranking' | 'mission' | 'collab';
  description?: string;
  startDate: Date;
  endDate: Date;
  image?: string;
  isActive: boolean;
  rewards?: any; // JSON data
  requirements?: any; // JSON data
  createdAt?: Date;
  updatedAt?: Date;
}

export interface NewEvent {
  id: string;
  name: string;
  type: 'festival' | 'gacha' | 'ranking' | 'mission' | 'collab';
  description?: string;
  startDate: Date;
  endDate: Date;
  image?: string;
  isActive: boolean;
  rewards?: any;
  requirements?: any;
}

// Bromides
export interface Bromide {
  id: string;
  name: string;
  type: 'Character' | 'Scene' | 'Frame' | 'Background' | 'Sticker' | 'Effect';
  rarity: 'N' | 'R' | 'SR' | 'SSR' | 'UR';
  description?: string;
  characterId?: string;
  effects?: any; // JSON data
  source?: string;
  image?: string;
  createdAt?: Date;
  updatedAt?: Date;
}

export interface NewBromide {
  id: string;
  name: string;
  type: 'Character' | 'Scene' | 'Frame' | 'Background' | 'Sticker' | 'Effect';
  rarity: 'N' | 'R' | 'SR' | 'SSR' | 'UR';
  description?: string;
  characterId?: string;
  effects?: any;
  source?: string;
  image?: string;
}

// Documents
export interface Document {
  id: string;
  title: string;
  content: string;
  category: string;
  tags?: string[];
  author: string;
  isPublished: boolean;
  createdAt?: Date;
  updatedAt?: Date;
}

export interface NewDocument {
  id: string;
  title: string;
  content: string;
  category: string;
  tags?: string[];
  author: string;
  isPublished: boolean;
}

// Memories
export interface Memory {
  id: string;
  title: string;
  description?: string;
  type: 'photo' | 'video';
  date: Date;
  characters?: string[];
  tags?: string[];
  thumbnail?: string;
  favorite: boolean;
  unlocked: boolean;
  createdAt?: Date;
  updatedAt?: Date;
}

export interface NewMemory {
  id: string;
  title: string;
  description?: string;
  type: 'photo' | 'video';
  date: Date;
  characters?: string[];
  tags?: string[];
  thumbnail?: string;
  favorite: boolean;
  unlocked: boolean;
}

// Shop Items
export interface ShopItem {
  id: string;
  name: string;
  description?: string;
  type: 'swimsuit' | 'accessory' | 'decoration' | 'currency' | 'booster';
  category: string;
  section: 'owner' | 'event' | 'venus' | 'vip';
  price: number;
  currency: 'coins' | 'gems' | 'tickets';
  rarity: 'common' | 'rare' | 'epic' | 'legendary';
  image?: string;
  inStock: boolean;
  isNew: boolean;
  discount?: number;
  limitedTime?: boolean;
  featured?: boolean;
  createdAt?: Date;
  updatedAt?: Date;
}

export interface NewShopItem {
  id: string;
  name: string;
  description?: string;
  type: 'swimsuit' | 'accessory' | 'decoration' | 'currency' | 'booster';
  category: string;
  section: 'owner' | 'event' | 'venus' | 'vip';
  price: number;
  currency: 'coins' | 'gems' | 'tickets';
  rarity: 'common' | 'rare' | 'epic' | 'legendary';
  image?: string;
  inStock: boolean;
  isNew: boolean;
  discount?: number;
  limitedTime?: boolean;
  featured?: boolean;
}
