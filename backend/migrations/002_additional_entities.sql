-- Additional entities migration for DOAXVV Handbook
-- Migration 002: Add missing entities (Bromides, Documents, Memories, Shop Items)

-- Bromides table
CREATE TABLE IF NOT EXISTS bromides (
  id VARCHAR(255) PRIMARY KEY,
  name VA<PERSON>HAR(255) NOT NULL,
  type VARCHAR(50) NOT NULL CHECK (type IN ('Character', 'Scene', 'Frame', 'Background', 'Sticker', 'Effect')),
  rarity VARCHAR(10) NOT NULL CHECK (rarity IN ('N', 'R', 'SR', 'SSR', 'UR')),
  description TEXT,
  character_id VARCHAR(255),
  effects JSONB, -- Store effects as JSON
  source VARCHAR(255),
  image VARCHAR(500),
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (character_id) REFERENCES characters(id) ON DELETE SET NULL
);

-- Documents table
CREATE TABLE IF NOT EXISTS documents (
  id VARCHAR(255) PRIMARY KEY,
  title VARCHAR(500) NOT NULL,
  content TEXT NOT NULL,
  category VARCHAR(100) NOT NULL,
  tags TEXT[], -- Array of tags
  author VARCHAR(255) NOT NULL,
  is_published BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Document Categories table
CREATE TABLE IF NOT EXISTS document_categories (
  id VARCHAR(100) PRIMARY KEY,
  name VARCHAR(255) NOT NULL,
  color VARCHAR(50) DEFAULT '#6B7280',
  description TEXT,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Memories table
CREATE TABLE IF NOT EXISTS memories (
  id VARCHAR(255) PRIMARY KEY,
  title VARCHAR(500) NOT NULL,
  description TEXT,
  type VARCHAR(20) NOT NULL CHECK (type IN ('photo', 'video')),
  date DATE NOT NULL,
  characters TEXT[], -- Array of character names/IDs
  tags TEXT[], -- Array of tags
  thumbnail VARCHAR(500),
  favorite BOOLEAN DEFAULT FALSE,
  unlocked BOOLEAN DEFAULT TRUE,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Shop Items table
CREATE TABLE IF NOT EXISTS shop_items (
  id VARCHAR(255) PRIMARY KEY,
  name VARCHAR(255) NOT NULL,
  description TEXT,
  type VARCHAR(50) NOT NULL CHECK (type IN ('swimsuit', 'accessory', 'decoration', 'currency', 'booster')),
  category VARCHAR(100) NOT NULL,
  section VARCHAR(20) NOT NULL CHECK (section IN ('owner', 'event', 'venus', 'vip')),
  price INTEGER NOT NULL CHECK (price >= 0),
  currency VARCHAR(20) NOT NULL CHECK (currency IN ('coins', 'gems', 'tickets')),
  rarity VARCHAR(20) NOT NULL CHECK (rarity IN ('common', 'rare', 'epic', 'legendary')),
  image VARCHAR(500),
  in_stock BOOLEAN DEFAULT TRUE,
  is_new BOOLEAN DEFAULT FALSE,
  discount INTEGER DEFAULT 0 CHECK (discount >= 0 AND discount <= 100),
  limited_time BOOLEAN DEFAULT FALSE,
  featured BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_bromides_type ON bromides(type);
CREATE INDEX IF NOT EXISTS idx_bromides_rarity ON bromides(rarity);
CREATE INDEX IF NOT EXISTS idx_bromides_character_id ON bromides(character_id);
CREATE INDEX IF NOT EXISTS idx_documents_category ON documents(category);
CREATE INDEX IF NOT EXISTS idx_documents_published ON documents(is_published);
CREATE INDEX IF NOT EXISTS idx_documents_author ON documents(author);
CREATE INDEX IF NOT EXISTS idx_memories_type ON memories(type);
CREATE INDEX IF NOT EXISTS idx_memories_date ON memories(date);
CREATE INDEX IF NOT EXISTS idx_memories_favorite ON memories(favorite);
CREATE INDEX IF NOT EXISTS idx_shop_items_type ON shop_items(type);
CREATE INDEX IF NOT EXISTS idx_shop_items_section ON shop_items(section);
CREATE INDEX IF NOT EXISTS idx_shop_items_rarity ON shop_items(rarity);
CREATE INDEX IF NOT EXISTS idx_shop_items_featured ON shop_items(featured);
CREATE INDEX IF NOT EXISTS idx_shop_items_in_stock ON shop_items(in_stock);

-- Full-text search indexes
CREATE INDEX IF NOT EXISTS idx_bromides_search ON bromides USING gin(to_tsvector('english', name || ' ' || COALESCE(description, '')));
CREATE INDEX IF NOT EXISTS idx_documents_search ON documents USING gin(to_tsvector('english', title || ' ' || content));
CREATE INDEX IF NOT EXISTS idx_memories_search ON memories USING gin(to_tsvector('english', title || ' ' || COALESCE(description, '')));
CREATE INDEX IF NOT EXISTS idx_shop_items_search ON shop_items USING gin(to_tsvector('english', name || ' ' || COALESCE(description, '')));

-- Insert default document categories
INSERT INTO document_categories (id, name, color, description) VALUES 
  ('tutorial', 'Tutorial', '#3B82F6', 'Step-by-step guides and tutorials'),
  ('reference', 'Reference', '#10B981', 'Reference materials and documentation'),
  ('gameplay', 'Gameplay', '#F59E0B', 'Gameplay strategies and tips'),
  ('update', 'Updates', '#EF4444', 'Game updates and patch notes'),
  ('community', 'Community', '#8B5CF6', 'Community resources and guides')
ON CONFLICT (id) DO NOTHING;

-- Insert migration record
INSERT INTO migrations (name) VALUES ('002_additional_entities') ON CONFLICT (name) DO NOTHING;

-- Update user statistics
INSERT INTO user_statistics (stat_name, stat_value) VALUES 
  ('total_bromides', 0),
  ('total_documents', 0),
  ('total_memories', 0),
  ('total_shop_items', 0)
ON CONFLICT (stat_name) DO NOTHING;
