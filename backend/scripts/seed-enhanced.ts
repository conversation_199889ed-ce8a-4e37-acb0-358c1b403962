import { config } from 'dotenv';
config();

import logger from '../config/logger';
import { testConnection } from '../config/database';
import databaseService from '../services/DatabaseService';

// Import frontend data
import { charactersData } from '../../frontend/data/characters';
import { swimsuitsData } from '../../frontend/data/swimsuits';
import { skillsData } from '../../frontend/data/skills';
import { accessoriesData } from '../../frontend/data/accessories';
import { eventsData } from '../../frontend/data/events';
import { bromidesData } from '../../frontend/data/bromides';

async function seedDatabase() {
  try {
    logger.info('🌱 Starting enhanced database seeding...');

    // Test database connection
    const dbConnected = await testConnection();
    if (!dbConnected) {
      logger.error('❌ Failed to connect to database');
      process.exit(1);
    }

    // Initialize database service
    await databaseService.initialize();

    // Seed Characters
    logger.info('📝 Seeding characters...');
    for (const character of charactersData) {
      try {
        await databaseService.createCharacter({
          id: character.id,
          name: character.name,
          nameJp: character.name, // Use same name for now
          nameEn: character.name,
          nameZh: character.name
        });
        logger.info(`✅ Created character: ${character.name}`);
      } catch (error: any) {
        if (error.message.includes('already exists')) {
          logger.info(`⚠️  Character ${character.name} already exists, skipping`);
        } else {
          logger.error(`❌ Failed to create character ${character.name}:`, error.message);
        }
      }
    }

    // Seed Skills
    logger.info('📝 Seeding skills...');
    for (const skill of skillsData) {
      try {
        await databaseService.createSkill({
          id: skill.id,
          name: skill.name,
          type: skill.type,
          description: skill.description
        });
        logger.info(`✅ Created skill: ${skill.name}`);
      } catch (error: any) {
        if (error.message.includes('already exists')) {
          logger.info(`⚠️  Skill ${skill.name} already exists, skipping`);
        } else {
          logger.error(`❌ Failed to create skill ${skill.name}:`, error.message);
        }
      }
    }

    // Seed Swimsuits
    logger.info('📝 Seeding swimsuits...');
    for (const swimsuit of swimsuitsData) {
      try {
        await databaseService.createSwimsuit({
          id: swimsuit.id,
          name: swimsuit.name,
          characterId: swimsuit.character.toLowerCase().replace(' ', ''), // Map to character ID
          rarity: swimsuit.rarity as 'SSR' | 'SR' | 'R',
          pow: swimsuit.stats.pow,
          tec: swimsuit.stats.tec,
          stm: swimsuit.stats.stm,
          apl: swimsuit.stats.apl,
          releaseDate: new Date(swimsuit.release),
          reappearDate: swimsuit.reappear ? new Date(swimsuit.reappear) : undefined
        });
        logger.info(`✅ Created swimsuit: ${swimsuit.name}`);
      } catch (error: any) {
        if (error.message.includes('already exists')) {
          logger.info(`⚠️  Swimsuit ${swimsuit.name} already exists, skipping`);
        } else {
          logger.error(`❌ Failed to create swimsuit ${swimsuit.name}:`, error.message);
        }
      }
    }

    // Seed Events
    logger.info('📝 Seeding events...');
    for (const event of eventsData) {
      try {
        await databaseService.createEvent({
          id: event.id,
          name: event.name,
          type: event.type,
          description: event.description,
          startDate: new Date(event.startDate),
          endDate: new Date(event.endDate),
          image: event.image,
          isActive: event.isActive,
          rewards: event.rewards,
          requirements: event.requirements
        });
        logger.info(`✅ Created event: ${event.name}`);
      } catch (error: any) {
        if (error.message.includes('already exists')) {
          logger.info(`⚠️  Event ${event.name} already exists, skipping`);
        } else {
          logger.error(`❌ Failed to create event ${event.name}:`, error.message);
        }
      }
    }

    // Seed Bromides
    logger.info('📝 Seeding bromides...');
    for (const bromide of bromidesData) {
      try {
        await databaseService.createBromide({
          id: bromide.id,
          name: bromide.name,
          type: bromide.type as any,
          rarity: bromide.rarity as any,
          description: bromide.description,
          characterId: bromide.character?.toLowerCase().replace(' ', '') || null,
          effects: bromide.effects,
          source: bromide.source
        });
        logger.info(`✅ Created bromide: ${bromide.name}`);
      } catch (error: any) {
        if (error.message.includes('already exists')) {
          logger.info(`⚠️  Bromide ${bromide.name} already exists, skipping`);
        } else {
          logger.error(`❌ Failed to create bromide ${bromide.name}:`, error.message);
        }
      }
    }

    logger.info('🎉 Enhanced database seeding completed successfully!');
    logger.info(`📊 Seeded:
    - ${charactersData.length} characters
    - ${skillsData.length} skills  
    - ${swimsuitsData.length} swimsuits
    - ${eventsData.length} events
    - ${bromidesData.length} bromides`);

  } catch (error) {
    logger.error('❌ Database seeding failed:', error);
    process.exit(1);
  } finally {
    process.exit(0);
  }
}

// Run the seeding
seedDatabase();
